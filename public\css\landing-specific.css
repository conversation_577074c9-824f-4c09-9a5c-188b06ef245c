/* Landing-specific styles extracted from inline styles in landing.html */

/* These styles were previously inline in landing.html and have been moved here
   to eliminate duplication and improve maintainability */

/* Note: Many of these styles may duplicate styles in main.css and other CSS files.
   They have been kept here for now to ensure the page continues to work correctly,
   but should be further consolidated in the future. */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #f8f6f3 0%, #ffffff 100%);
    min-height: 100vh;
    color: #333;
    overflow-x: hidden;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 20px 0;
    z-index: 1000;
    border-bottom: 1px solid rgba(139, 69, 19, 0.1);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 20px;
}

.logo {
    font-size: 28px;
    font-weight: 700;
    background: linear-gradient(45deg, #8B4513, #D2691E);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    cursor: pointer;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 100px 20px 60px;
}

.hero-content {
    max-width: 600px;
    animation: fadeInUp 1s ease-out;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    background: linear-gradient(45deg, #8B4513, #D2691E);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.3rem;
    color: #666;
    margin-bottom: 40px;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.hero-btn {
    background: linear-gradient(45deg, #8B4513, #D2691E);
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 30px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
    text-decoration: none;
    display: inline-block;
    box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
}

.hero-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(139, 69, 19, 0.4);
}

.hero-btn.secondary {
    background: transparent;
    color: #8B4513;
    border: 2px solid #8B4513;
    box-shadow: none;
}

.hero-btn.secondary:hover {
    background: #8B4513;
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        padding: 0 15px;
    }

    .hero {
        padding: 80px 15px 40px;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-btn {
        width: 100%;
        max-width: 300px;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Authentication Modal */
.auth-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 2000;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.auth-modal-content {
    background: white;
    border-radius: 20px;
    width: 100%;
    max-width: 400px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

.auth-modal-header {
    padding: 30px 30px 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.auth-modal-header h3 {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 28px;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: #f5f5f5;
    color: #666;
}

.auth-modal-body {
    padding: 20px 30px 30px;
}

.auth-form {
    display: block;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #fafafa;
}

.form-group input:focus {
    outline: none;
    border-color: #8B4513;
    background: white;
    box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
}

.form-group input.error {
    border-color: #e74c3c;
    background: #fdf2f2;
}

.form-group input.error:focus {
    border-color: #e74c3c;
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.form-group input.success {
    border-color: #27ae60;
    background: #f2fdf2;
}

.form-group input.success:focus {
    border-color: #27ae60;
    box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.1);
}

.field-error {
    color: #e74c3c;
    font-size: 12px;
    margin-top: 5px;
    display: none;
}

.field-success {
    color: #27ae60;
    font-size: 12px;
    margin-top: 5px;
    display: none;
}

.auth-btn {
    width: 100%;
    background: linear-gradient(45deg, #8B4513, #D2691E);
    color: white;
    border: none;
    padding: 14px;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.auth-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(139, 69, 19, 0.3);
}

.auth-btn:active {
    transform: translateY(0);
}

.auth-switch {
    text-align: center;
    color: #666;
    font-size: 14px;
    margin: 0;
}

.auth-switch a {
    color: #8B4513;
    text-decoration: none;
    font-weight: 600;
}

.auth-switch a:hover {
    text-decoration: underline;
}

.auth-error {
    background: #fee;
    color: #c33;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 14px;
    border: 1px solid #fcc;
}

.auth-loading {
    text-align: center;
    padding: 20px;
    color: #666;
}

.loading-spinner {
    width: 30px;
    height: 30px;
    border: 3px solid #f0f0f0;
    border-top: 3px solid #8B4513;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Mobile responsiveness for modal */
@media (max-width: 768px) {
    .auth-modal {
        padding: 10px;
    }

    .auth-modal-content {
        max-width: 100%;
        border-radius: 15px;
    }

    .auth-modal-header {
        padding: 20px 20px 15px;
    }

    .auth-modal-header h3 {
        font-size: 20px;
    }

    .auth-modal-body {
        padding: 15px 20px 20px;
    }
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}