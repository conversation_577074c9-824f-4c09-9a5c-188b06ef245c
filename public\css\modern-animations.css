/* Modern Animations & Micro-interactions for Naroop 2024-2025 */

/* ===== KEYFRAME ANIMATIONS ===== */

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 5px var(--color-primary);
    }
    50% {
        box-shadow: 0 0 20px var(--color-primary), 0 0 30px var(--color-primary);
    }
}

/* ===== UTILITY ANIMATION CLASSES ===== */

.fade-in-up {
    animation: fadeInUp 0.6s var(--ease-out-cubic) forwards;
}

.fade-in-down {
    animation: fadeInDown 0.6s var(--ease-out-cubic) forwards;
}

.slide-in-left {
    animation: slideInLeft 0.5s var(--ease-out-cubic) forwards;
}

.slide-in-right {
    animation: slideInRight 0.5s var(--ease-out-cubic) forwards;
}

.scale-in {
    animation: scaleIn 0.4s var(--ease-out-cubic) forwards;
}

.pulse-animation {
    animation: pulse 2s infinite;
}

.bounce-on-click {
    animation: bounce 0.6s;
}

.glow-effect {
    animation: glow 2s ease-in-out infinite alternate;
}

/* ===== HOVER EFFECTS ===== */

.hover-lift {
    transition: transform var(--transition-smooth), box-shadow var(--transition-smooth);
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.hover-scale {
    transition: transform var(--transition-smooth);
}

.hover-scale:hover {
    transform: scale(1.02);
}

.hover-glow {
    transition: box-shadow var(--transition-smooth);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
}

.hover-brightness {
    transition: filter var(--transition-smooth);
}

.hover-brightness:hover {
    filter: brightness(1.1);
}

/* ===== LOADING ANIMATIONS ===== */

.loading-skeleton {
    background: linear-gradient(90deg, var(--bg-card) 25%, rgba(255, 255, 255, 0.1) 50%, var(--bg-card) 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
    border-radius: var(--radius-lg);
}

.loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid var(--border-primary);
    border-top: 2px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-dots {
    display: inline-flex;
    gap: 4px;
}

.loading-dots span {
    width: 6px;
    height: 6px;
    background: var(--color-primary);
    border-radius: 50%;
    animation: loadingDots 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes loadingDots {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* ===== NOTIFICATION ANIMATIONS ===== */

.notification-pulse {
    animation: pulse 1s ease-in-out;
}

.notification-slide-in {
    animation: slideInRight 0.3s var(--ease-out-cubic);
}

.notification-slide-out {
    animation: slideInRight 0.3s var(--ease-out-cubic) reverse;
}

/* ===== STAGGER ANIMATIONS ===== */

.stagger-children > * {
    opacity: 0;
    animation: fadeInUp 0.6s var(--ease-out-cubic) forwards;
}

.stagger-children > *:nth-child(1) { animation-delay: 0.1s; }
.stagger-children > *:nth-child(2) { animation-delay: 0.2s; }
.stagger-children > *:nth-child(3) { animation-delay: 0.3s; }
.stagger-children > *:nth-child(4) { animation-delay: 0.4s; }
.stagger-children > *:nth-child(5) { animation-delay: 0.5s; }
.stagger-children > *:nth-child(6) { animation-delay: 0.6s; }

/* ===== FOCUS ANIMATIONS ===== */

.focus-ring {
    position: relative;
}

.focus-ring:focus-visible::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid var(--color-primary);
    border-radius: inherit;
    animation: focusRing 0.3s ease-out;
}

@keyframes focusRing {
    0% {
        transform: scale(0.95);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* ===== RIPPLE EFFECT ===== */

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    animation: rippleEffect 0.6s linear;
    pointer-events: none;
}

@keyframes rippleEffect {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* ===== ENHANCED BUTTON STATES ===== */

.btn-base {
    position: relative;
    overflow: hidden;
}

.btn-base:active {
    transform: scale(0.98);
}

/* ===== SCROLL REVEAL ANIMATIONS ===== */

.reveal-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
}

.reveal-on-scroll.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* ===== ENHANCED CARD INTERACTIONS ===== */

.card-interactive {
    cursor: pointer;
    transition: all var(--transition-smooth);
}

.card-interactive:hover {
    transform: translateY(-4px) scale(1.01);
    box-shadow: var(--shadow-lg);
}

.card-interactive:active {
    transform: translateY(-2px) scale(0.99);
}

/* ===== FLOATING ELEMENTS ===== */

.float {
    animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* ===== REDUCED MOTION SUPPORT ===== */

@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .loading-skeleton {
        animation: none;
        background: var(--bg-card);
    }

    .ripple {
        display: none;
    }

    .float {
        animation: none;
    }
}
