/* Enhanced Responsive Design for Naroop - Modern Mobile-First Approach */

/* Large Desktop Styles (1400px+) */
@media (min-width: 1400px) {
    .main-content {
        grid-template-columns: 320px 1fr 360px;
        gap: var(--spacing-8);
        max-width: 1600px;
        margin: 0 auto;
    }

    .sidebar {
        width: 320px;
    }
}

/* Desktop Styles (1200px - 1399px) */
@media (max-width: 1399px) and (min-width: 1200px) {
    .main-content {
        grid-template-columns: 280px 1fr 320px;
        gap: var(--spacing-6);
    }
}

/* Tablet Large Styles (1024px - 1199px) */
@media (max-width: 1199px) and (min-width: 1024px) {
    .main-content {
        grid-template-columns: 250px 1fr 280px;
        gap: var(--spacing-5);
        padding: 0 var(--spacing-4);
    }

    .sidebar {
        width: 250px;
        padding: var(--spacing-5);
    }

    .header-content {
        padding: 0 var(--spacing-5);
    }
}

/* Small Tablet / Large Mobile */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 0;
    }
    
    .header {
        padding: 15px 20px;
        margin-bottom: 20px;
        border-radius: 15px;
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .logo {
        font-size: 24px;
    }
    
    .nav-buttons {
        width: 100%;
        justify-content: center;
    }
    
    .nav-btn {
        flex: 1;
        max-width: 120px;
        padding: 8px 16px;
        font-size: 13px;
    }
    
    /* Hide desktop sidebar on mobile */
    .sidebar {
        display: none;
    }
    
    /* Show mobile navigation */
    .mobile-nav {
        display: flex;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        padding: 10px 0;
        z-index: 1000;
        justify-content: space-around;
        align-items: center;
    }
    
    .mobile-nav-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 8px 12px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        color: #6b7280;
        font-size: 12px;
        font-weight: 500;
        text-decoration: none;
        min-width: 60px;
    }
    
    .mobile-nav-item:hover,
    .mobile-nav-item.active {
        background: rgba(139, 69, 19, 0.1);
        color: #8B4513;
    }
    
    .mobile-nav-item::before {
        font-size: 20px;
        margin-bottom: 4px;
    }
    
    .mobile-nav-item:nth-child(1)::before { content: "🏠"; }
    .mobile-nav-item:nth-child(2)::before { content: "🔍"; }
    .mobile-nav-item:nth-child(3)::before { content: "💬"; }
    .mobile-nav-item:nth-child(4)::before { content: "👤"; }
    
    /* Add bottom padding to main content for mobile nav */
    .main-content {
        padding-bottom: 80px;
    }
    
    /* Feed adjustments */
    .feed-section {
        max-width: 100%;
    }
    
    .feed-header {
        margin-bottom: 20px;
        padding-bottom: 12px;
    }
    
    .feed-title {
        font-size: 24px;
    }
    
    .refresh-btn {
        padding: 8px 12px;
        font-size: 13px;
    }
    
    /* Post cards mobile optimization */
    .post-card {
        padding: 16px;
        margin-bottom: 16px;
        border-radius: 12px;
    }
    
    .post-header {
        margin-bottom: 12px;
    }
    
    .author-avatar {
        width: 36px;
        height: 36px;
        font-size: 14px;
    }
    
    .author-name {
        font-size: 13px;
    }
    
    .post-time {
        font-size: 11px;
    }
    
    .post-title {
        font-size: 16px;
        margin-bottom: 6px;
    }
    
    .post-text {
        font-size: 14px;
    }
    
    .post-actions {
        padding-top: 10px;
        margin-bottom: 6px;
    }
    
    .action-btn {
        padding: 6px 10px;
        font-size: 13px;
    }
    
    .action-icon {
        font-size: 14px;
    }
    
    .action-text {
        font-size: 12px;
    }
    
    /* Create post mobile */
    .create-post {
        padding: 16px;
        margin-bottom: 20px;
        border-radius: 12px;
    }
    
    .create-post-header {
        gap: 12px;
        margin-bottom: 16px;
    }
    
    .create-post-icon {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }
    
    .create-post h3 {
        font-size: 16px;
    }
    
    .create-post p {
        font-size: 13px;
    }
    
    .create-btn {
        padding: 10px 20px;
        font-size: 13px;
        margin-top: 12px;
    }
}

/* Mobile Large Styles (481px - 767px) */
@media (max-width: 767px) and (min-width: 481px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
        padding: var(--spacing-4);
        padding-bottom: 80px; /* Space for mobile nav */
    }

    .sidebar {
        position: fixed;
        top: var(--header-height-mobile);
        left: -100%;
        width: 280px;
        height: calc(100vh - var(--header-height-mobile));
        z-index: var(--z-modal);
        transition: left var(--transition-smooth);
        border-radius: 0;
        border-left: none;
        border-top: none;
        border-bottom: none;
    }

    .sidebar.open {
        left: 0;
    }

    .trending {
        display: none; /* Hide trending sidebar on mobile */
    }

    .header-content {
        padding: 0 var(--spacing-4);
    }

    .logo {
        font-size: var(--font-size-xl);
    }
}

/* Mobile Small Styles (320px - 480px) */
@media (max-width: 480px) {
    .container {
        padding: var(--spacing-3);
    }

    .header {
        height: var(--header-height-mobile);
    }

    .header-content {
        padding: 0 var(--spacing-3);
    }

    .logo {
        font-size: var(--font-size-lg);
    }

    .main-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-3);
        padding: var(--spacing-3);
        padding-bottom: 80px;
    }

    .sidebar {
        width: 100vw;
        padding: var(--spacing-4);
    }

    .nav-item {
        height: 52px; /* Larger touch targets */
        padding: var(--spacing-3) var(--spacing-4);
        font-size: var(--font-size-base);
    }

    .nav-icon {
        width: 24px;
        height: 24px;
        font-size: 24px;
    }

    .btn-base {
        min-height: 48px; /* Better touch targets */
        padding: var(--spacing-3) var(--spacing-5);
    }

    .btn-sm {
        min-height: 40px;
        padding: var(--spacing-2) var(--spacing-4);
    }

    .btn-lg {
        min-height: 56px;
        padding: var(--spacing-4) var(--spacing-6);
    }

    .card-base {
        border-radius: var(--radius-xl);
        padding: var(--spacing-4);
    }

    .feed-title {
        font-size: var(--font-size-xl);
    }

    .post-card {
        padding: var(--spacing-4);
        margin-bottom: var(--spacing-3);
        border-radius: var(--radius-lg);
    }

    .create-post-btn {
        width: 100%;
        justify-content: center;
    }
}

/* ===== MOBILE NAVIGATION BAR ===== */
@media (max-width: 767px) {
    .mobile-nav {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: var(--header-bg);
        backdrop-filter: var(--header-backdrop-blur);
        border-top: var(--header-border);
        padding: var(--spacing-2) 0;
        z-index: var(--z-sticky);
        display: flex;
        justify-content: space-around;
        align-items: center;
        height: 72px;
    }

    .mobile-nav-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-1);
        padding: var(--spacing-2);
        border-radius: var(--radius-lg);
        cursor: pointer;
        transition: all var(--transition-smooth);
        color: var(--text-muted);
        text-decoration: none;
        min-width: 60px;
        font-size: var(--font-size-xs);
        font-weight: var(--font-weight-medium);
    }

    .mobile-nav-item:hover,
    .mobile-nav-item.active {
        color: var(--color-primary);
        background: var(--bg-card);
        transform: translateY(-2px);
    }

    .mobile-nav-icon {
        width: 24px;
        height: 24px;
        font-size: 20px;
        transition: all var(--transition-smooth);
    }

    .mobile-nav-item:hover .mobile-nav-icon,
    .mobile-nav-item.active .mobile-nav-icon {
        transform: scale(1.1);
    }

    /* Hide desktop sidebar toggle on mobile */
    .sidebar-toggle {
        display: none;
    }
}

/* Hide mobile nav on desktop */
@media (min-width: 768px) {
    .mobile-nav {
        display: none;
    }
}
    
    .mobile-nav-item {
        padding: 6px 8px;
        font-size: 11px;
        min-width: 50px;
    }
    
    .mobile-nav-item::before {
        font-size: 18px;
        margin-bottom: 2px;
    }
}

/* Large Desktop */
@media (min-width: 1200px) {
    .main-content {
        grid-template-columns: 280px 1fr 280px;
        gap: 30px;
        max-width: 1200px;
        margin: 0 auto;
    }
    
    .sidebar {
        padding: 30px;
    }
    
    .feed-section {
        max-width: 650px;
    }
}

/* Ultra-wide screens */
@media (min-width: 1600px) {
    .main-content {
        max-width: 1400px;
        grid-template-columns: 320px 1fr 320px;
        gap: 40px;
    }
    
    .feed-section {
        max-width: 700px;
    }
}

/* Landscape mobile orientation */
@media (max-width: 768px) and (orientation: landscape) {
    .header {
        padding: 10px 20px;
        margin-bottom: 15px;
    }
    
    .main-content {
        padding-bottom: 60px;
    }
    
    .mobile-nav {
        padding: 6px 0;
    }
    
    .mobile-nav-item {
        padding: 4px 8px;
        font-size: 10px;
    }
    
    .mobile-nav-item::before {
        font-size: 16px;
        margin-bottom: 2px;
    }
}

/* Print styles */
@media print {
    .header,
    .sidebar,
    .mobile-nav,
    .create-post,
    .post-actions,
    .refresh-btn,
    .load-more {
        display: none !important;
    }
    
    .main-content {
        grid-template-columns: 1fr;
        gap: 0;
        padding: 0;
    }
    
    .post-card {
        box-shadow: none;
        border: 1px solid #ddd;
        margin-bottom: 20px;
        break-inside: avoid;
    }
    
    .post-card:hover {
        transform: none;
    }
    
    body {
        background: white;
    }
}

/* High DPI / Retina displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .logo,
    .post-title,
    .feed-title {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* Dark mode support (if implemented) */
@media (prefers-color-scheme: dark) {
    /* Dark mode styles would go here */
    /* Currently not implemented but structure is ready */
}
