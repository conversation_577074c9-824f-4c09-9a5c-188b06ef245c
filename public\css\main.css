/* Main Styles for Naroop Social Media Platform - Modern Dark Theme */

/* ===== ENHANCED COMPONENT BASE CLASSES ===== */

/* Modern Card Base - Enhanced glassmorphism styling */
.card-base {
    background: var(--bg-card);
    border-radius: var(--radius-2xl);
    border: var(--card-border);
    backdrop-filter: var(--card-backdrop-blur);
    box-shadow: var(--shadow-card);
    transition: all var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.card-base::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    opacity: 0;
    transition: opacity var(--transition-smooth);
}

.card-base:hover {
    background: var(--bg-card-hover);
    transform: var(--transform-translate-hover);
    box-shadow: var(--shadow-card-hover);
    border-color: var(--border-hover);
}

.card-base:hover::before {
    opacity: 1;
}

.card-base:active {
    transform: var(--transform-translate-active);
    box-shadow: var(--shadow-card-active);
}

/* Enhanced Card Padding Variants */
.card-padding {
    padding: var(--card-padding);
}

.card-padding-sm {
    padding: var(--card-padding-sm);
}

.card-padding-lg {
    padding: var(--card-padding-lg);
}

/* Card elevation variants */
.card-elevated {
    background: var(--bg-card-elevated);
    box-shadow: var(--shadow-lg);
}

.card-glass {
    background: var(--bg-glass);
    backdrop-filter: var(--backdrop-blur-lg);
    box-shadow: var(--shadow-glass);
}

/* Card Positioning */
.card-sticky {
    position: sticky;
    top: 110px;
    height: fit-content;
}

/* Enhanced Button Base Classes - Modern social media styling */
.btn-base {
    border: none;
    border-radius: var(--button-border-radius);
    font-family: var(--font-family-primary);
    font-weight: var(--button-font-weight);
    cursor: pointer;
    transition: all var(--transition-smooth);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    text-decoration: none;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
}

.btn-base::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-smooth);
}

.btn-base:hover::before {
    left: 100%;
}

.btn-base:focus-visible {
    outline: 2px solid var(--border-accent);
    outline-offset: 2px;
}

.btn-base:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Enhanced Button Variants */
.btn-primary {
    background: var(--gradient-button);
    color: var(--text-primary);
    box-shadow: var(--shadow-primary);
    font-weight: var(--font-weight-semibold);
}

.btn-primary:hover:not(:disabled) {
    background: var(--gradient-button-hover);
    transform: var(--transform-translate-hover);
    box-shadow: var(--shadow-primary-lg);
}

.btn-primary:active:not(:disabled) {
    transform: var(--transform-scale-active);
    box-shadow: var(--shadow-primary);
}

.btn-secondary {
    background: var(--bg-card);
    color: var(--text-secondary);
    border: 1px solid var(--border-secondary);
    backdrop-filter: var(--backdrop-blur-sm);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--bg-card-hover);
    border-color: var(--border-hover);
    color: var(--text-primary);
    transform: var(--transform-translate-hover);
}

.btn-ghost {
    background: transparent;
    color: var(--text-muted);
    border: 1px solid transparent;
}

.btn-ghost:hover:not(:disabled) {
    background: var(--bg-card);
    color: var(--text-primary);
    border-color: var(--border-primary);
}

/* Enhanced Button Size Variants */
.btn-sm {
    height: var(--button-height-sm);
    padding: var(--button-padding-sm);
    font-size: var(--font-size-sm);
    gap: var(--spacing-1);
}

.btn-md {
    height: var(--button-height-md);
    padding: var(--button-padding-md);
    font-size: var(--font-size-base);
}

.btn-lg {
    height: var(--button-height-lg);
    padding: var(--button-padding-lg);
    font-size: var(--font-size-lg);
    gap: var(--spacing-3);
}

/* Button with icon */
.btn-icon {
    aspect-ratio: 1;
    padding: 0;
}

.btn-icon .icon {
    width: var(--nav-icon-size);
    height: var(--nav-icon-size);
}

/* Reset and Base Styles - Updated for modern dark theme */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-primary);
    background: var(--bg-primary);
    min-height: 100vh;
    color: var(--text-primary);
    line-height: var(--line-height-normal);
    overflow-x: hidden;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    letter-spacing: var(--letter-spacing-normal);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* ===== ENHANCED TYPOGRAPHY CLASSES ===== */

/* Heading Styles */
h1, .text-4xl {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    letter-spacing: var(--letter-spacing-tight);
    color: var(--text-primary);
}

h2, .text-3xl {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    letter-spacing: var(--letter-spacing-tight);
    color: var(--text-primary);
}

h3, .text-2xl {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-snug);
    letter-spacing: var(--letter-spacing-normal);
    color: var(--text-primary);
}

h4, .text-xl {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-snug);
    color: var(--text-primary);
}

h5, .text-lg {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
    color: var(--text-primary);
}

h6, .text-base {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    line-height: var(--line-height-normal);
    color: var(--text-primary);
}

/* Text Size Utilities */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }
.text-5xl { font-size: var(--font-size-5xl); }
.text-6xl { font-size: var(--font-size-6xl); }

/* Font Weight Utilities */
.font-thin { font-weight: var(--font-weight-thin); }
.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }
.font-extrabold { font-weight: var(--font-weight-extrabold); }
.font-black { font-weight: var(--font-weight-black); }

/* Text Color Utilities */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-subtle { color: var(--text-subtle); }
.text-accent { color: var(--text-accent); }
.text-success { color: var(--text-success); }
.text-warning { color: var(--text-warning); }
.text-error { color: var(--text-error); }

/* Line Height Utilities */
.leading-none { line-height: var(--line-height-none); }
.leading-tight { line-height: var(--line-height-tight); }
.leading-snug { line-height: var(--line-height-snug); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }
.leading-loose { line-height: var(--line-height-loose); }

/* Letter Spacing Utilities */
.tracking-tighter { letter-spacing: var(--letter-spacing-tighter); }
.tracking-tight { letter-spacing: var(--letter-spacing-tight); }
.tracking-normal { letter-spacing: var(--letter-spacing-normal); }
.tracking-wide { letter-spacing: var(--letter-spacing-wide); }
.tracking-wider { letter-spacing: var(--letter-spacing-wider); }
.tracking-widest { letter-spacing: var(--letter-spacing-widest); }

/* Container and Layout - Updated for modern design */
.container {
    max-width: var(--breakpoint-2xl);
    margin: 0 auto;
    padding: 0 var(--spacing-6);
}

/* Grid and Flexbox Layouts */
.card-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
}

.flex-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

/* Enhanced Header Styles - Modern glassmorphism design */
.header {
    background: var(--header-bg);
    backdrop-filter: var(--header-backdrop-blur);
    border-bottom: var(--header-border);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    height: var(--header-height);
    transition: all var(--transition-smooth);
}

@media (max-width: 768px) {
    .header {
        height: var(--header-height-mobile);
    }
}

/* Enhanced Header Content */
.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    max-width: var(--breakpoint-2xl);
    margin: 0 auto;
    padding: 0 var(--spacing-6);
}

.logo {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    cursor: pointer;
    transition: all var(--transition-smooth);
    user-select: none;
    letter-spacing: var(--letter-spacing-tight);
}

.logo:hover {
    transform: var(--transform-scale-hover);
    filter: var(--filter-brightness-hover);
}

@media (max-width: 768px) {
    .header-content {
        padding: 0 var(--spacing-4);
    }

    .logo {
        font-size: var(--font-size-xl);
    }
}

/* Navigation Buttons - Updated for modern theme */
.nav-buttons {
    display: flex;
    gap: var(--spacing-lg);
    align-items: center;
}

.nav-btn {
    background: var(--gradient-button);
    color: var(--text-primary);
    border: none;
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--radius-2xl);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    transition: var(--transition-normal);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    font-size: var(--font-size-base);
    box-shadow: var(--shadow-glow);
}

.nav-btn:hover {
    background: var(--gradient-button-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow-strong);
}

.nav-btn.primary {
    background: var(--gradient-button);
    color: var(--text-primary);
}

.nav-btn.primary:hover {
    background: var(--gradient-button-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow-strong);
}

/* Main Content Layout - Updated for modern design */
.main-content {
    display: grid;
    grid-template-columns: var(--sidebar-width) 1fr 320px;
    gap: var(--spacing-2xl);
    padding: var(--spacing-2xl) 0;
    min-height: calc(100vh - var(--header-height));
}

/* Enhanced Sidebar Styles - Modern glassmorphism design */
.sidebar {
    background: var(--sidebar-bg);
    border-radius: var(--radius-2xl);
    padding: var(--card-padding);
    height: fit-content;
    position: sticky;
    top: calc(var(--header-height) + var(--spacing-6));
    border: var(--card-border);
    backdrop-filter: var(--sidebar-backdrop-blur);
    transition: all var(--transition-smooth);
    width: var(--sidebar-width);
}

.sidebar:hover {
    background: var(--bg-card-hover);
    border-color: var(--border-hover);
}

.sidebar h3 {
    color: var(--color-primary);
    margin-bottom: var(--spacing-6);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    letter-spacing: var(--letter-spacing-wide);
}

@media (max-width: 1024px) {
    .sidebar {
        width: 250px;
        top: calc(var(--header-height-mobile) + var(--spacing-4));
    }
}

@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: var(--header-height-mobile);
        left: -100%;
        width: var(--sidebar-width-mobile);
        height: calc(100vh - var(--header-height-mobile));
        z-index: var(--z-modal);
        transition: left var(--transition-smooth);
        border-radius: 0;
    }

    .sidebar.open {
        left: 0;
    }
}

/* Enhanced Navigation Items - Modern social media design */
.nav-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    height: var(--nav-item-height);
    padding: var(--nav-item-padding);
    margin-bottom: var(--spacing-2);
    border-radius: var(--nav-item-border-radius);
    cursor: pointer;
    transition: all var(--transition-smooth);
    border: 1px solid transparent;
    position: relative;
    overflow: hidden;
    font-weight: var(--font-weight-medium);
}

.nav-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 3px;
    height: 100%;
    background: var(--color-primary);
    transform: scaleY(0);
    transition: transform var(--transition-smooth);
    border-radius: 0 2px 2px 0;
}

.nav-item:hover {
    background: var(--bg-card-hover);
    border-color: var(--border-hover);
    transform: var(--transform-translate-hover);
    color: var(--text-primary);
}

.nav-item:hover::before {
    transform: scaleY(1);
}

.nav-item.active {
    background: var(--gradient-card-hover);
    border-color: var(--border-accent);
    color: var(--text-primary);
    font-weight: var(--font-weight-semibold);
}

.nav-item.active::before {
    transform: scaleY(1);
}

.nav-item:focus-visible {
    outline: 2px solid var(--border-accent);
    outline-offset: 2px;
}

.nav-icon {
    width: var(--nav-icon-size);
    height: var(--nav-icon-size);
    font-size: var(--nav-icon-size);
    color: var(--color-primary);
    transition: all var(--transition-smooth);
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-item:hover .nav-icon,
.nav-item.active .nav-icon {
    color: var(--color-accent);
    transform: var(--transform-scale-hover);
}

/* Legacy sidebar-item support */
.sidebar-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-lg) var(--spacing-xl);
    cursor: pointer;
    transition: var(--transition-normal);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-sm);
    border: 1px solid transparent;
}

.sidebar-item:hover {
    background: var(--bg-card-hover);
    border-color: var(--border-focus);
    transform: translateX(var(--spacing-xs));
}

.sidebar-item.active {
    background: var(--gradient-card);
    border-color: var(--border-accent);
}

/* Feed Section - Updated for modern dark theme */
.feed-section, .feed {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    padding: 0;
    border: var(--card-border);
    backdrop-filter: var(--card-backdrop-blur);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.feed-header {
    padding: var(--card-padding);
    border-bottom: var(--card-border);
}

.feed-title {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-lg);
    background: linear-gradient(45deg, #ffffff, var(--color-primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Refresh Button - Updated for modern dark theme */
.refresh-btn {
    background: var(--bg-input);
    color: var(--text-muted);
    border: var(--input-border);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-lg);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    transition: var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    box-shadow: var(--shadow-sm);
}

.refresh-btn:hover {
    background: var(--bg-card-hover);
    color: var(--color-primary);
    border-color: var(--border-accent);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.refresh-btn::before {
    content: '🔄';
    font-size: var(--font-size-sm);
}

/* Content Sections */
.content-section {
    transition: opacity 0.3s ease-in-out;
    opacity: 1;
}

.content-section:not(.active) {
    display: none !important;
    opacity: 0;
}

.content-section.active,
.feed.content-section.active {
    display: flex !important;
    flex-direction: column;
    opacity: 1;
    min-height: 400px;
}

/* Mobile Navigation */
.mobile-nav-item {
    transition: all 0.3s ease;
}

.mobile-nav-item.touch-active {
    background: rgba(139, 69, 19, 0.2);
    transform: scale(0.95);
}

/* Loading States - Updated for modern theme */
.loading-spinner {
    width: 30px;
    height: 30px;
    border: 3px solid var(--border-primary);
    border-top: 3px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility Classes */
.text-center { text-align: center; }
.hidden { display: none !important; }
.visible { display: block !important; }

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Focus styles for accessibility - Updated for modern theme */
.nav-item:focus,
.sidebar-item:focus,
.nav-btn:focus,
.refresh-btn:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

/* High contrast mode support - Updated for modern theme */
@media (prefers-contrast: high) {
    .nav-item:hover,
    .sidebar-item:hover {
        background: var(--bg-card-hover);
        border-color: var(--border-accent);
    }

    .nav-btn {
        border-width: 3px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    html {
        scroll-behavior: auto;
    }
}

/* Modern Modal System - Updated for dark theme */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-overlay);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity var(--transition-normal), visibility var(--transition-normal);
    z-index: var(--z-modal-backdrop);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--bg-card);
    padding: var(--card-padding);
    border-radius: var(--radius-xl);
    border: var(--card-border);
    backdrop-filter: var(--card-backdrop-blur);
    width: 90%;
    max-width: 500px;
    position: relative;
    transform: scale(0.95);
    transition: transform var(--transition-normal);
    box-shadow: var(--shadow-xl);
}

.modal-overlay.active .modal-content {
    transform: scale(1);
}

.modal-close-button {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    background: var(--bg-input);
    border: var(--input-border);
    border-radius: var(--radius-full);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    cursor: pointer;
    color: var(--text-primary);
    transition: var(--transition-normal);
}

.modal-close-button:hover {
    background: var(--bg-card-hover);
    border-color: var(--border-accent);
}
