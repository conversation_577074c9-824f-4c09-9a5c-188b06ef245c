/* CSS Variables for Naroop - Modern Design System 2024-2025 */

:root {
    /* ===== ENHANCED BRAND COLORS ===== */
    --color-primary: #ff6b35;
    --color-primary-light: #ff8555;
    --color-primary-dark: #e55a2b;
    --color-accent: #f7931e;
    --color-accent-light: #ffb347;
    --color-accent-dark: #e0841a;
    --color-accent-secondary: #ffd700;
    --color-accent-tertiary: #ffb347;

    /* Modern neutral colors for better contrast */
    --color-neutral-50: #fafafa;
    --color-neutral-100: #f5f5f5;
    --color-neutral-200: #e5e5e5;
    --color-neutral-300: #d4d4d4;
    --color-neutral-400: #a3a3a3;
    --color-neutral-500: #737373;
    --color-neutral-600: #525252;
    --color-neutral-700: #404040;
    --color-neutral-800: #262626;
    --color-neutral-900: #171717;
    --color-neutral-950: #0a0a0a;

    /* ===== ENHANCED GRADIENTS ===== */
    --gradient-primary: linear-gradient(135deg, var(--color-primary) 0%, var(--color-accent) 50%, var(--color-accent-secondary) 100%);
    --gradient-primary-subtle: linear-gradient(135deg, rgba(255, 107, 53, 0.8) 0%, rgba(247, 147, 30, 0.8) 100%);
    --gradient-dark: linear-gradient(135deg, #0a0a0a 0%, #171717 25%, #262626 75%, #171717 100%);
    --gradient-dark-subtle: linear-gradient(135deg, rgba(10, 10, 10, 0.95) 0%, rgba(23, 23, 23, 0.95) 100%);
    --gradient-card: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%);
    --gradient-card-hover: linear-gradient(135deg, rgba(255, 107, 53, 0.12) 0%, rgba(247, 147, 30, 0.08) 100%);
    --gradient-button: linear-gradient(135deg, var(--color-primary) 0%, var(--color-accent) 100%);
    --gradient-button-hover: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-accent-light) 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);

    /* ===== ENHANCED TEXT COLORS ===== */
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.92);
    --text-muted: rgba(255, 255, 255, 0.85);
    --text-subtle: rgba(255, 255, 255, 0.75);
    --text-disabled: rgba(255, 255, 255, 0.5);
    --text-inverse: var(--color-neutral-900);
    --text-accent: var(--color-accent);
    --text-success: #10b981;
    --text-warning: #f59e0b;
    --text-error: #ef4444;

    /* ===== ENHANCED BACKGROUND COLORS ===== */
    --bg-primary: var(--gradient-dark);
    --bg-secondary: var(--color-neutral-900);
    --bg-card: rgba(255, 255, 255, 0.06);
    --bg-card-elevated: rgba(255, 255, 255, 0.08);
    --bg-card-hover: rgba(255, 107, 53, 0.12);
    --bg-card-active: rgba(255, 107, 53, 0.15);
    --bg-input: rgba(255, 255, 255, 0.08);
    --bg-input-focus: rgba(255, 255, 255, 0.12);
    --bg-input-hover: rgba(255, 255, 255, 0.1);
    --bg-overlay: rgba(0, 0, 0, 0.85);
    --bg-overlay-light: rgba(0, 0, 0, 0.6);
    --bg-glass: var(--gradient-glass);

    /* ===== ENHANCED BORDER COLORS ===== */
    --border-primary: rgba(255, 255, 255, 0.08);
    --border-secondary: rgba(255, 255, 255, 0.12);
    --border-tertiary: rgba(255, 255, 255, 0.16);
    --border-accent: var(--color-primary);
    --border-focus: rgba(255, 107, 53, 0.4);
    --border-hover: rgba(255, 255, 255, 0.2);
    --border-active: rgba(255, 107, 53, 0.6);
    --border-success: #10b981;
    --border-warning: #f59e0b;
    --border-error: #ef4444;

    /* ===== ENHANCED SPACING SYSTEM ===== */
    --spacing-0: 0;
    --spacing-px: 1px;
    --spacing-0_5: 0.125rem;  /* 2px */
    --spacing-1: 0.25rem;     /* 4px */
    --spacing-1_5: 0.375rem;  /* 6px */
    --spacing-2: 0.5rem;      /* 8px */
    --spacing-2_5: 0.625rem;  /* 10px */
    --spacing-3: 0.75rem;     /* 12px */
    --spacing-3_5: 0.875rem;  /* 14px */
    --spacing-4: 1rem;        /* 16px */
    --spacing-5: 1.25rem;     /* 20px */
    --spacing-6: 1.5rem;      /* 24px */
    --spacing-7: 1.75rem;     /* 28px */
    --spacing-8: 2rem;        /* 32px */
    --spacing-9: 2.25rem;     /* 36px */
    --spacing-10: 2.5rem;     /* 40px */
    --spacing-11: 2.75rem;    /* 44px */
    --spacing-12: 3rem;       /* 48px */
    --spacing-14: 3.5rem;     /* 56px */
    --spacing-16: 4rem;       /* 64px */
    --spacing-20: 5rem;       /* 80px */
    --spacing-24: 6rem;       /* 96px */
    --spacing-28: 7rem;       /* 112px */
    --spacing-32: 8rem;       /* 128px */

    /* Legacy spacing for compatibility */
    --spacing-xs: var(--spacing-1);
    --spacing-sm: var(--spacing-2);
    --spacing-md: var(--spacing-4);
    --spacing-lg: var(--spacing-6);
    --spacing-xl: var(--spacing-8);
    --spacing-2xl: var(--spacing-12);
    --spacing-3xl: var(--spacing-16);

    /* ===== ENHANCED BORDER RADIUS ===== */
    --radius-none: 0;
    --radius-xs: 0.125rem;    /* 2px */
    --radius-sm: 0.25rem;     /* 4px */
    --radius-md: 0.375rem;    /* 6px */
    --radius-lg: 0.5rem;      /* 8px */
    --radius-xl: 0.75rem;     /* 12px */
    --radius-2xl: 1rem;       /* 16px */
    --radius-3xl: 1.5rem;     /* 24px */
    --radius-4xl: 2rem;       /* 32px */
    --radius-full: 9999px;

    /* ===== ENHANCED TYPOGRAPHY SYSTEM ===== */
    --font-family-primary: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-family-secondary: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
    --font-family-mono: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', monospace;

    /* Enhanced Fluid Typography using clamp() - Better scaling */
    --font-size-xs: clamp(0.75rem, 0.69rem + 0.31vw, 0.875rem);     /* 12-14px */
    --font-size-sm: clamp(0.875rem, 0.81rem + 0.31vw, 1rem);        /* 14-16px */
    --font-size-base: clamp(1rem, 0.94rem + 0.31vw, 1.125rem);      /* 16-18px */
    --font-size-lg: clamp(1.125rem, 1.06rem + 0.31vw, 1.25rem);     /* 18-20px */
    --font-size-xl: clamp(1.25rem, 1.19rem + 0.31vw, 1.375rem);     /* 20-22px */
    --font-size-2xl: clamp(1.5rem, 1.38rem + 0.63vw, 1.875rem);     /* 24-30px */
    --font-size-3xl: clamp(1.875rem, 1.69rem + 0.94vw, 2.25rem);    /* 30-36px */
    --font-size-4xl: clamp(2.25rem, 2rem + 1.25vw, 3rem);           /* 36-48px */
    --font-size-5xl: clamp(3rem, 2.63rem + 1.88vw, 4rem);           /* 48-64px */
    --font-size-6xl: clamp(3.75rem, 3.19rem + 2.81vw, 5.25rem);     /* 60-84px */

    /* Enhanced Font Weights */
    --font-weight-thin: 100;
    --font-weight-extralight: 200;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --font-weight-black: 900;

    /* Line Heights */
    --line-height-none: 1;
    --line-height-tight: 1.25;
    --line-height-snug: 1.375;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.625;
    --line-height-loose: 2;

    /* Letter Spacing */
    --letter-spacing-tighter: -0.05em;
    --letter-spacing-tight: -0.025em;
    --letter-spacing-normal: 0em;
    --letter-spacing-wide: 0.025em;
    --letter-spacing-wider: 0.05em;
    --letter-spacing-widest: 0.1em;
    
    /* Line Heights */
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.6;
    --line-height-loose: 1.8;
    
    /* ===== ENHANCED SHADOW SYSTEM ===== */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.08);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 1px 2px 0 rgba(0, 0, 0, 0.08);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.12), 0 2px 4px -1px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.15), 0 4px 6px -2px rgba(0, 0, 0, 0.08);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.15), 0 10px 10px -5px rgba(0, 0, 0, 0.06);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-3xl: 0 35px 60px -12px rgba(0, 0, 0, 0.3);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.08);
    --shadow-none: 0 0 #0000;

    /* Enhanced Colored Shadows for modern depth */
    --shadow-primary: 0 10px 15px -3px rgba(255, 107, 53, 0.15), 0 4px 6px -2px rgba(255, 107, 53, 0.08);
    --shadow-primary-lg: 0 20px 25px -5px rgba(255, 107, 53, 0.2), 0 10px 10px -5px rgba(255, 107, 53, 0.1);
    --shadow-accent: 0 10px 15px -3px rgba(247, 147, 30, 0.15), 0 4px 6px -2px rgba(247, 147, 30, 0.08);
    --shadow-accent-lg: 0 20px 25px -5px rgba(247, 147, 30, 0.2), 0 10px 10px -5px rgba(247, 147, 30, 0.1);
    --shadow-glow: 0 0 20px rgba(255, 107, 53, 0.3);
    --shadow-glow-strong: 0 0 30px rgba(255, 107, 53, 0.5);

    /* Glassmorphism shadows */
    --shadow-glass: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
    --shadow-glass-lg: 0 16px 40px 0 rgba(0, 0, 0, 0.4);

    /* Modern card shadows */
    --shadow-card: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-card-hover: 0 4px 12px 0 rgba(0, 0, 0, 0.15), 0 2px 6px 0 rgba(0, 0, 0, 0.1);
    --shadow-card-active: 0 1px 2px 0 rgba(0, 0, 0, 0.1);

    /* Legacy shadow variable for compatibility */
    --color-shadow: rgba(0, 0, 0, 0.1);

    /* ===== LEGACY BORDER RADIUS (for compatibility) ===== */
    --border-radius-sm: var(--radius-sm);
    --border-radius-md: var(--radius-md);
    --border-radius-lg: var(--radius-lg);
    --border-radius-xl: var(--radius-xl);

    /* ===== LEGACY COLOR VARIABLES (for compatibility) ===== */
    --color-text: var(--text-primary);
    --color-background: var(--bg-primary);
    
    /* ===== Z-INDEX SCALE ===== */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
    
    /* ===== ENHANCED TRANSITIONS & ANIMATIONS ===== */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Modern transition curves */
    --transition-smooth: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-snappy: 0.15s cubic-bezier(0.4, 0, 1, 1);
    --transition-gentle: 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    /* Backdrop filters for glassmorphism */
    --backdrop-blur-xs: blur(2px);
    --backdrop-blur-sm: blur(4px);
    --backdrop-blur-md: blur(8px);
    --backdrop-blur-lg: blur(12px);
    --backdrop-blur-xl: blur(16px);
    --backdrop-blur-2xl: blur(24px);
    --backdrop-blur-3xl: blur(40px);

    /* Modern filter effects */
    --filter-brightness-hover: brightness(1.1);
    --filter-brightness-active: brightness(0.95);
    --filter-saturate-hover: saturate(1.2);
    --filter-blur-loading: blur(1px);

    /* Transform utilities */
    --transform-scale-hover: scale(1.02);
    --transform-scale-active: scale(0.98);
    --transform-translate-hover: translateY(-2px);
    --transform-translate-active: translateY(0px);
    
    /* ===== BREAKPOINTS (for reference in JS) ===== */
    --breakpoint-xs: 320px;
    --breakpoint-sm: 480px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1200px;
    --breakpoint-2xl: 1400px;
    
    /* ===== ENHANCED COMPONENT SPECIFIC ===== */
    /* Header */
    --header-height: 72px;
    --header-height-mobile: 64px;
    --header-bg: rgba(0, 0, 0, 0.85);
    --header-backdrop-blur: var(--backdrop-blur-xl);
    --header-border: 1px solid var(--border-primary);

    /* Sidebar */
    --sidebar-width: 280px;
    --sidebar-width-collapsed: 72px;
    --sidebar-width-mobile: 100vw;
    --sidebar-bg: var(--bg-card);
    --sidebar-backdrop-blur: var(--backdrop-blur-lg);

    /* Cards */
    --card-padding: var(--spacing-6);
    --card-padding-sm: var(--spacing-4);
    --card-padding-lg: var(--spacing-8);
    --card-border: 1px solid var(--border-primary);
    --card-backdrop-blur: var(--backdrop-blur-md);
    --card-min-height: 120px;

    /* Buttons */
    --button-height-sm: 36px;
    --button-height-md: 44px;
    --button-height-lg: 52px;
    --button-padding-sm: var(--spacing-2) var(--spacing-4);
    --button-padding-md: var(--spacing-3) var(--spacing-6);
    --button-padding-lg: var(--spacing-4) var(--spacing-8);
    --button-border-radius: var(--radius-lg);
    --button-font-weight: var(--font-weight-medium);

    /* Forms */
    --input-height: 48px;
    --input-height-sm: 40px;
    --input-height-lg: 56px;
    --input-padding: var(--spacing-3) var(--spacing-4);
    --input-padding-lg: var(--spacing-4) var(--spacing-6);
    --input-border: 1px solid var(--border-secondary);
    --input-border-focus: 2px solid var(--border-accent);
    --input-border-radius: var(--radius-lg);
    --input-font-size: var(--font-size-base);

    /* Modal */
    --modal-backdrop: rgba(0, 0, 0, 0.8);
    --modal-backdrop-blur: var(--backdrop-blur-sm);
    --modal-border-radius: var(--radius-2xl);
    --modal-padding: var(--spacing-8);
    --modal-max-width: 480px;

    /* Navigation */
    --nav-item-height: 48px;
    --nav-item-padding: var(--spacing-3) var(--spacing-4);
    --nav-item-border-radius: var(--radius-xl);
    --nav-icon-size: 20px;
    
    /* ===== ANIMATION CURVES ===== */
    --ease-in-quad: cubic-bezier(0.55, 0.085, 0.68, 0.53);
    --ease-in-cubic: cubic-bezier(0.55, 0.055, 0.675, 0.19);
    --ease-out-quad: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --ease-out-cubic: cubic-bezier(0.215, 0.61, 0.355, 1);
    --ease-in-out-quad: cubic-bezier(0.455, 0.03, 0.515, 0.955);
    --ease-in-out-cubic: cubic-bezier(0.645, 0.045, 0.355, 1);
}

/* ===== DARK MODE OVERRIDES ===== */
@media (prefers-color-scheme: dark) {
    :root {
        /* Enhanced dark mode variables if needed */
        --text-muted: rgba(255, 255, 255, 0.85);
        --bg-card: rgba(255, 255, 255, 0.08);
    }
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
    :root {
        --text-muted: rgba(255, 255, 255, 0.95);
        --text-subtle: rgba(255, 255, 255, 0.9);
        --border-primary: rgba(255, 255, 255, 0.3);
        --border-secondary: rgba(255, 255, 255, 0.4);
    }
}

/* ===== REDUCED MOTION ===== */
@media (prefers-reduced-motion: reduce) {
    :root {
        --transition-fast: 0.01ms;
        --transition-normal: 0.01ms;
        --transition-slow: 0.01ms;
        --transition-bounce: 0.01ms;
    }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */

/* CSS Containment for better performance */
.card-base {
    contain: layout style;
}

.feed, .sidebar, .trending {
    contain: layout style paint;
}

/* Will-change for animated elements */
.nav-item,
.trending-item,
.create-post-btn {
    will-change: transform, background-color;
}

.nav-item:not(:hover),
.trending-item:not(:hover),
.create-post-btn:not(:hover) {
    will-change: auto;
}

/* GPU acceleration for smooth animations */
.card-base:hover,
.nav-item:hover,
.trending-item:hover,
.create-post-btn:hover {
    transform: translateZ(0);
}

/* Optimize font rendering */
body {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Optimize backdrop-filter performance */
.header,
.card-base {
    -webkit-backdrop-filter: var(--header-backdrop-blur);
    backdrop-filter: var(--header-backdrop-blur);
    transform: translateZ(0);
}
