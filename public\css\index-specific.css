/* Index-specific styles extracted from inline styles in index.html */

/* These styles were previously inline in index.html and have been moved here
   to eliminate duplication and improve maintainability */

/* Note: Many of these styles may duplicate styles in main.css and other CSS files.
   They have been kept here for now to ensure the page continues to work correctly,
   but should be further consolidated in the future. */

/* Header */
.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) 0;
}

.search-container {
    position: relative;
    flex: 1;
    max-width: 400px;
    margin: 0 var(--spacing-2xl);
}

.search-input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg) var(--spacing-md) 50px;
    background: var(--bg-input);
    border: var(--input-border);
    border-radius: var(--radius-2xl);
    color: var(--text-primary);
    font-size: var(--font-size-base);
    transition: var(--transition-normal);
}

.search-input:focus {
    outline: none;
    background: var(--bg-input-focus);
    border-color: var(--border-accent);
    box-shadow: var(--shadow-glow);
}

.search-input::placeholder {
    color: var(--text-subtle);
}

.search-icon {
    position: absolute;
    left: 18px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.75);
}

.user-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.notification-btn, .preferences-btn {
    position: relative;
    padding: var(--spacing-md);
    background: var(--bg-input);
    border: var(--input-border);
    border-radius: var(--radius-full);
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition-normal);
    font-size: var(--font-size-lg);
}

.notification-btn:hover, .preferences-btn:hover {
    background: var(--bg-card-hover);
    border-color: var(--border-accent);
    transform: scale(1.05);
}

.profile-img {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    background: var(--gradient-button);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-bold);
    cursor: pointer;
    transition: var(--transition-normal);
    border: 2px solid var(--border-accent);
    box-shadow: var(--shadow-glow);
}

.profile-img:hover {
    transform: scale(1.1);
}

/* Story Prompt */
.story-prompt {
    background: var(--gradient-card);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    border: 1px solid rgba(255, 107, 53, 0.3);
    margin-bottom: var(--spacing-lg);
}

.story-prompt h4 {
    color: var(--color-primary);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-lg);
}

.story-prompt p {
    color: var(--text-muted);
    margin-bottom: var(--spacing-lg);
    line-height: var(--line-height-relaxed);
}

.create-post-btn {
    background: var(--gradient-button);
    color: var(--text-primary);
    border: none;
    padding: var(--spacing-lg) var(--spacing-2xl);
    border-radius: var(--radius-2xl);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    transition: var(--transition-normal);
    font-size: var(--font-size-base);
    width: 100%;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-glow);
}

.create-post-btn:hover {
    background: var(--gradient-button-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow-strong);
}

.create-post-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.create-post-btn:hover::before {
    left: 100%;
}

/* Load More */
.load-more {
    text-align: center;
    padding: var(--spacing-lg);
    color: var(--text-muted);
    cursor: pointer;
    transition: var(--transition-normal);
    border-radius: var(--radius-lg);
    margin: var(--spacing-lg);
    background: var(--bg-input);
    border: var(--input-border);
}

.load-more:hover {
    color: var(--color-primary);
    background: var(--bg-card-hover);
    border-color: var(--border-accent);
}

/* Empty State Styling */
.empty-state {
    text-align: center;
    padding: var(--spacing-3xl);
    background: var(--gradient-card);
    border-radius: var(--radius-xl);
    border: 1px solid rgba(255, 107, 53, 0.2);
    margin: var(--spacing-lg);
}

.empty-state h4 {
    color: var(--color-primary);
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-md);
    font-weight: var(--font-weight-semibold);
}

.empty-state p {
    color: var(--text-muted);
    line-height: var(--line-height-relaxed);
    max-width: 400px;
    margin: 0 auto;
}

/* Loading State Styling */
.loading-state {
    padding: var(--spacing-lg);
}

/* Section Content Styles */
.explore-content, .messages-content, .profile-content, .communities-content, .analytics-content {
    padding: 20px;
    text-align: center;
}

.explore-content h3, .messages-content h3, .profile-content h3, .communities-content h3, .analytics-content h3 {
    color: #ff6b35;
    margin-bottom: 15px;
    font-size: 24px;
}

.explore-content p, .messages-content p, .communities-content p, .analytics-content p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 20px;
    line-height: 1.6;
}

/* Explore Categories */
.explore-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    margin-top: 20px;
}

.category-tag {
    background: rgba(255, 107, 53, 0.2);
    color: #ff6b35;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 107, 53, 0.3);
}

.category-tag:hover {
    background: rgba(255, 107, 53, 0.3);
    transform: translateY(-2px);
}

/* Profile Styles */
.profile-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    justify-content: center;
}

.profile-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(45deg, #ff6b35, #f7931e);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    font-weight: bold;
}

.profile-info h3 {
    color: #ffffff;
    margin-bottom: 5px;
    font-size: 24px;
}

.profile-info p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 16px;
}

.profile-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-bottom: 30px;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 28px;
    font-weight: bold;
    color: #ff6b35;
    margin-bottom: 5px;
}

.stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
}

/* Screen Reader Only */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}